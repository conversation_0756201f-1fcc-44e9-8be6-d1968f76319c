"""LangGraph single-node graph template.

Returns a predefined response. Replace logic and configuration as needed.
"""

from __future__ import annotations

import logging
from typing import Any, Dict, TypedDict, Annotated

from langchain.retrievers import EnsembleRetriever
from langchain_community.document_loaders import CSVLoader
from langchain_community.retrievers import BM25Retriever
from langchain_community.vectorstores import Chroma, FAISS
from langchain_core.messages import HumanMessage, BaseMessage
from langchain_core.vectorstores import InMemoryVectorStore
from langchain_openai import ChatOpenAI, OpenAIEmbeddings
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langgraph.constants import END, START
from langgraph.graph import StateGraph, add_messages
from langgraph.prebuilt import ToolNode
from langgraph.runtime import Runtime

BASE_URL = "https://api.siliconflow.cn"
EMBDDING_URL = "https://api.siliconflow.cn/v1"
API_KEY = "sk-kobigkzhoynflbglwvtpjecdkaihcjfieqspeuahifvublck"
# MODEL = "deepseek-ai/DeepSeek-R1"
MODEL = "deepseek-ai/DeepSeek-V3"
# MODEL = "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B"
# MODEL = "Qwen/Qwen2.5-7B-Instruct"
EmbeddingModel = "Qwen/Qwen3-Embedding-8B"
# EmbeddingModel = "BAAI/bge-m3"
MCP_TOKEN = "mcp_m2m_vR1nI9aiaqRCv8Lj6OT0XdPgmvDtccQzpv8Kdc2k6oY_96e802af942bdd84"

logging.basicConfig(
    level=logging.DEBUG,  # 设置最低显示级别：DEBUG/INFO/WARNING/ERROR/CRITICAL
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()  # 输出到控制台
    ]
)

class Context(TypedDict):
    """Context parameters for the agent.

    Set these when creating assistants OR when invoking the graph.
    See: https://langchain-ai.github.io/langgraph/cloud/how-tos/configuration_cloud/
    """

    my_configurable_param: str


class State(TypedDict):
    messages: Annotated[list[BaseMessage], add_messages]


embeddings_model = OpenAIEmbeddings(base_url=EMBDDING_URL, api_key=API_KEY, model=EmbeddingModel)

def csvLoad1():
    # 1. 加载 CSV
    loader = CSVLoader(file_path="/Users/<USER>/workspace/cloudyigou/customer-ai/src/agent/plt.csv", encoding="utf-8")
    documents = loader.load()

    # 2. 文本拆分（可选）
    splitter = RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=50)
    doc_splits = splitter.split_documents(documents)

    # 2. 初始化BM25检索器（关键词粗筛）
    bm25_retriever = BM25Retriever.from_documents(doc_splits)
    bm25_retriever.k = 5  # 召回Top5

    # 3. 初始化向量检索器（语义精排）
    vectordb = Chroma.from_documents(doc_splits, embedding=embeddings_model, persist_directory="./chroma_db")
    vector_retriever = vectordb.as_retriever(search_kwargs={"k": 5})

    # 4. 混合检索器（权重可调整，向量检索权重更高，如0.6）
    ensemble_retriever = EnsembleRetriever(
        retrievers=[bm25_retriever, vector_retriever],
        weights=[0.4, 0.6]  # 向量检索权重更高，优先语义匹配
    )

    return ensemble_retriever

def csvLoad2():
    # 1. 加载 CSV
    loader = CSVLoader(file_path="/Users/<USER>/workspace/cloudyigou/customer-ai/src/agent/plt.csv", encoding="utf-8")
    documents = loader.load()

    # 2. 文本拆分（可选）
    splitter = RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=50)
    doc_splits = splitter.split_documents(documents)

    vectorstore = InMemoryVectorStore.from_documents(
        documents=doc_splits,
        embedding=embeddings_model,
    )

    return vectorstore

def csvLoad3():
    loader = CSVLoader(
        file_path="/Users/<USER>/workspace/cloudyigou/customer-ai/src/agent/plt.csv",
        source_column="feedback",  # 用作embedding的文本
        metadata_columns=["analysis", "solution"]
    )
    documents = loader.load()

    vectorstore = FAISS.from_documents(documents, embeddings_model)

    return vectorstore

def csvLoad4():
    loader = CSVLoader(
        file_path="/Users/<USER>/workspace/cloudyigou/customer-ai/src/agent/plt.csv", encoding="utf-8"
    )
    documents = loader.load()

    vectorstore = FAISS.from_documents(documents, embeddings_model)

    return vectorstore

model = ChatOpenAI(base_url=BASE_URL, api_key=API_KEY, model=MODEL)

# retriever = csvLoad1()
# retriever = csvLoad2()
# retriever = csvLoad3()
retriever = csvLoad4()

def call_model(state: State, runtime: Runtime[Context]) -> Dict[str, Any]:
    logging.info(f"进入call_model……")

    # print(state["messages"][-1])
    # print(type(state["messages"][-1]))

    msg = state["messages"][-1].content[-1]['text']

    # loader1:
    # relevant_docs = retriever.similarity_search_with_score(msg)
    # print(relevant_docs)
    # if len(relevant_docs) == 0:
    #     answer = model.invoke(state["messages"])
    #     return {"messages": [answer]}

    # loader2:
    # docs_and_scores = retriever.similarity_search_with_score(msg, k=1)
    # threshold = 0.3
    # filtered_docs = [
    #     doc for doc, score in docs_and_scores
    #     if score >= threshold
    # ]
    # if len(filtered_docs) == 0:
    #     answer = model.invoke(state["messages"])
    #     return {"messages": [answer]}

    # loader3:
    docs_and_scores = retriever.similarity_search_with_score(msg, k=5)

    logging.info(f"召回的向量: {docs_and_scores}")

    # 设定相似度阈值（以 OpenAI embedding + cosine 为例，越接近1越相似）
    SIMILARITY_THRESHOLD = 0.6

    # 过滤掉低匹配的文档
    relevant_docs = [doc for doc, score in docs_and_scores if score >= SIMILARITY_THRESHOLD]
    if not relevant_docs:
        logging.info(f"没有采纳向量……")
        answer = model.invoke(state["messages"])
    else:
        # doc = "\n".join([doc.page_content + "\n" +
        #                      f"分析: {doc.metadata['analysis']}\n"
        #                      f"处理: {doc.metadata['solution']}" for doc in relevant_docs])
        doc = "\n".join([doc.page_content for doc in relevant_docs])
        logging.info(f"采纳的向量: {doc}")
        answer = model.invoke(state["messages"] + [HumanMessage(content=doc)])

    return {"messages": [answer]}

# Define the graph
graph = (
    StateGraph(State, context_schema=Context)
    .add_node(call_model)
    .add_edge("__start__", "call_model")
    .compile(name="New Graph")
)


from IPython.display import Image, display
from langchain_core.runnables.graph import CurveStyle, MermaidDrawMethod, NodeStyles

try:
    display(Image(graph.get_graph().draw_png()))
except ImportError:
    print(
        "You likely need to install dependencies for pygraphviz, see more here https://github.com/pygraphviz/pygraphviz/blob/main/INSTALL.txt"
    )